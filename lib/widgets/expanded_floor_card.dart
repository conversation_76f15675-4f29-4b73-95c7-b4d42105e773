import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/routes.dart';
import 'package:songanh/theme.dart';

enum RoomType {
  deluxe,
  suite,
  vipstandard

}

class ExpandedFloorCard extends StatefulWidget {
  const ExpandedFloorCard({super.key, required this.rooms});
  final List<Room> rooms;

  @override
  State<ExpandedFloorCard> createState() => _ExpandedFloorCardState();
}

class _ExpandedFloorCardState extends State<ExpandedFloorCard> {
  late final ValueNotifier<Item> valItem;
  @override
  void initState() {
    valItem = ValueNotifier(
      Item(room: widget.rooms),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: valItem,
        builder: (context, vRoom, child) {
          final roomActive = vRoom.room
              .where((final r) => r.isActive == 'TRUE');
          final total = roomActive.fold(
              0, (l, r) => l + (int.tryParse(r.gia ?? '') ?? 0));
          return _buildFloor(valItem, roomActive, total);
        });
  }

  ValueListenableBuilder<Item> _buildFloor(
      ValueNotifier<Item> valItem, Iterable<Room> roomActive, int total) {
    return ValueListenableBuilder(
      valueListenable: valItem,
      builder: (context, vItem, child) {
        return Container(
          width: MediaQuery.sizeOf(context).width - 24,
          margin: EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: AppTheme.cardGradient,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 16,
                offset: Offset(0, 4),
              ),
            ],
            border: Border.all(
              color: Colors.grey.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.primaryBlue.withValues(alpha: 0.1),
                      AppTheme.primaryLight.withValues(alpha: 0.05),
                    ],
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryBlue,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.primaryBlue.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.apartment_rounded,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        SizedBox(width: 16),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Tầng ${vItem.room.first.floor}',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: AppTheme.textPrimary,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              '${roomActive.length} phòng đang hoạt động',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    if (roomActive.isNotEmpty)
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          gradient: AppTheme.successGradient,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.successColor.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          '+ ${NumberFormat('#,###', 'vi_VN').format(total)} VND',
                          style: Theme.of(context).textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),
              ),

              // Expansion tile
              Theme(
                data: Theme.of(context).copyWith(
                  dividerColor: Colors.transparent,
                ),
                child: ExpansionTile(
                  tilePadding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                  childrenPadding: EdgeInsets.only(bottom: 16),
                  showTrailingIcon: roomActive.isNotEmpty,
                  iconColor: AppTheme.primaryBlue,
                  collapsedIconColor: AppTheme.textSecondary,
                  title: Text(
                    roomActive.isEmpty ? 'Không có phòng nào đang hoạt động' : 'Danh sách phòng',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: roomActive.isNotEmpty ? GestureDetector(
                    onTap: () {
                      AppNavigator.pushDetailFloor(context, widget.rooms);
                    },
                    child: Container(
                      margin: EdgeInsets.only(top: 8),
                      child: Text(
                        'Xem chi tiết →',
                        style: TextStyle(
                          color: AppTheme.primaryBlue,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ) : null,
                onExpansionChanged: (value) {
                  valItem.value = vItem.copyWith(
                    isExpanded: !vItem.isExpanded,
                  );
                },
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        for (final active in roomActive)
                          Container(
                            margin: EdgeInsets.only(bottom: 12),
                            padding: EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: AppTheme.successColor.withValues(alpha: 0.2),
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.04),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          padding: EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: AppTheme.successColor,
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: Icon(
                                            Icons.hotel_rounded,
                                            color: Colors.white,
                                            size: 16,
                                          ),
                                        ),
                                        SizedBox(width: 12),
                                        Text(
                                          'Phòng ${active.roomId}',
                                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                            fontWeight: FontWeight.w700,
                                            color: AppTheme.textPrimary,
                                          ),
                                        ),
                                        SizedBox(width: 8),
                                        _buildRoomTypeBadge(active.type),
                                      ],
                                    ),
                                    if (active.gia != null)
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                        decoration: BoxDecoration(
                                          gradient: AppTheme.successGradient,
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Text(
                                          '+${NumberFormat('#,###', 'vi_VN').format(int.tryParse(active.gia ?? '') ?? 0)} VND',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            color: Colors.white,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                if (active.timeIn?.isNotEmpty == true || active.timeOut?.isNotEmpty == true) ...[
                                  SizedBox(height: 12),
                                  Row(
                                    children: [
                                      if (active.timeIn?.isNotEmpty == true) ...[
                                        Icon(
                                          Icons.login_rounded,
                                          size: 16,
                                          color: AppTheme.textSecondary,
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          'Nhận: ${_formatTime(active.timeIn ?? '')}',
                                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                            color: AppTheme.textSecondary,
                                          ),
                                        ),
                                      ],
                                      if (active.timeIn?.isNotEmpty == true && active.timeOut?.isNotEmpty == true)
                                        Padding(
                                          padding: EdgeInsets.symmetric(horizontal: 12),
                                          child: Container(
                                            width: 1,
                                            height: 12,
                                            color: AppTheme.dividerColor,
                                          ),
                                        ),
                                      if (active.timeOut?.isNotEmpty == true) ...[
                                        Icon(
                                          Icons.logout_rounded,
                                          size: 16,
                                          color: AppTheme.textSecondary,
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          'Trả: ${_formatTime(active.timeOut ?? '')}',
                                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                            color: AppTheme.textSecondary,
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ],
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
          )],
          ),
        );
      },
    );
  }

  Widget _buildRoomTypeBadge(String? type) {
    if (type == null) return SizedBox.shrink();

    final roomType = type.replaceAll(' ', '').toUpperCase();
    Color badgeColor;
    String label;

    switch (roomType) {
      case 'VIPSTANDARD':
        badgeColor = Color(0xFFFFB74D);
        label = 'VIP';
        break;
      case 'DELUXE':
        badgeColor = Color(0xFF4FC3F7);
        label = 'DELUXE';
        break;
      case 'SUITE':
        badgeColor = Color(0xFF9C27B0);
        label = 'SUITE';
        break;
      default:
        return SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: badgeColor.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  String _formatTime(String time) {
    try {
      DateTime dateTime = DateFormat('HH:mm dd/MM/yyyy').parse(time).toLocal();
      return DateFormat('HH:mm - dd/MM/yyyy').format(dateTime);
    } catch (e) {
      return time;
    }
  }


}

class Item {
  final List<Room> room;
  bool isExpanded;

  Item({required this.room, this.isExpanded = false});

  Item copyWith({List<Room>? room, bool? isExpanded}) {
    return Item(
      room: room ?? this.room,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}
