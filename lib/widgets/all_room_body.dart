import 'dart:ui' as ui;

import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/widgets/consumer_card.dart';
import 'package:songanh/widgets/expanded_floor_card.dart';
import 'package:songanh/widgets/room_card.dart';
import 'package:songanh/theme.dart';

class AllRoomBody extends StatelessWidget {
  const AllRoomBody({super.key, required this.list});
  final List<Room> list;
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        itemCount: 4,
        itemBuilder: (_, i) {
          final rooms = list.where(
            (element) => element.floor == (i + 1).toString(),
          );
          final total =
              rooms.fold(0, (l, r) => l + (int.tryParse(r.gia ?? '') ?? 0));
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: AppTheme.cardGradient,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 16,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Header
                Container(
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppTheme.primaryBlue.withValues(alpha: 0.1),
                        AppTheme.primaryLight.withValues(alpha: 0.05),
                      ],
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryBlue,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.primaryBlue.withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.apartment_rounded,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          SizedBox(width: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Tầng ${rooms.first.floor}',
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  color: AppTheme.textPrimary,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                '${rooms.length} phòng',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      if (total > 0)
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            gradient: AppTheme.successGradient,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.successColor.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            '+ ${NumberFormat('#,###', 'vi_VN').format(total)} VND',
                            style: Theme.of(context).textTheme.labelLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Room grid
                Padding(
                  padding: EdgeInsets.all(20),
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      ...List.generate(rooms.length, (i2) {
                    return RoomCard(
                      room: rooms.toList()[i2],
                      isOpen:rooms.toList()[i2].isActive == 'TRUE'?  (){
                            showModal(
          context: context,
          builder: (builder) {
            final room = rooms.toList()[i2];
            return AlertDialog(
              insetPadding: EdgeInsets.zero,
              actionsPadding: EdgeInsets.zero,
              title: Text.rich(TextSpan(children: [
                TextSpan(text: 'Phòng ${rooms.toList()[i2].roomId}'),
                TextSpan(text: ' '),
                     if (room.type?.replaceAll(' ', '') == RoomType.vipstandard.name.toUpperCase())
                          WidgetSpan(
                            alignment: ui.PlaceholderAlignment.middle,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                  color: Colors.yellow.shade200,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Text('VIP STANDARD'),
                              ),
                            ),
                          )
                         else if(room.type?.replaceAll(' ', '') == RoomType.deluxe.name.toUpperCase()) 
                         WidgetSpan(
                            alignment: ui.PlaceholderAlignment.middle,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                  color: Colors.cyan.shade200,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Text('DELUXE'),
                              ),
                            ),
                          )  else if(room.type?.replaceAll(' ', '') == RoomType.suite.name.toUpperCase()) 
                         WidgetSpan(
                            alignment: ui.PlaceholderAlignment.middle,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                  color: Colors.lightBlueAccent.shade200,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Text('SUITE'),
                              ),
                            ),
                          ),
              ])),
              content: SizedBox(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height / 3,
                child: ConsumerCard(room: rooms.toList()[i2]),
              ),
            );
          },
        );
                      }: (){
                        
                      },
                    );
                  })
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }
}
