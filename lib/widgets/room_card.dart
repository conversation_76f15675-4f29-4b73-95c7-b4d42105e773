import 'package:flutter/material.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/theme.dart';

class RoomCard extends StatefulWidget {
  const RoomCard({super.key, required this.room, required this.isOpen});
  final Room room;
  final Function() isOpen;

  @override
  State<RoomCard> createState() => _RoomCardState();
}

class _RoomCardState extends State<RoomCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getRoomTypeColor() {
    final roomType = widget.room.type?.replaceAll(' ', '').toUpperCase();
    switch (roomType) {
      case 'VIPSTANDARD':
        return Color(0xFFFFB74D); // Orange
      case 'DELUXE':
        return Color(0xFF4FC3F7); // Light Blue
      case 'SUITE':
        return Color(0xFF9C27B0); // Purple
      default:
        return AppTheme.primaryBlue;
    }
  }

  String _getRoomTypeLabel() {
    final roomType = widget.room.type?.replaceAll(' ', '').toUpperCase();
    switch (roomType) {
      case 'VIPSTANDARD':
        return 'VIP';
      case 'DELUXE':
        return 'DELUXE';
      case 'SUITE':
        return 'SUITE';
      default:
        return 'STANDARD';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isActive = widget.room.isActive == 'TRUE';
    final roomTypeColor = _getRoomTypeColor();

    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        _animationController.reverse();
        widget.isOpen();
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 100,
              height: 100,
              margin: EdgeInsets.all(4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: isActive
                  ? AppTheme.successGradient
                  : LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        Colors.grey.shade50,
                      ],
                    ),
                boxShadow: [
                  BoxShadow(
                    color: isActive
                      ? AppTheme.successColor.withValues(alpha: 0.3)
                      : Colors.black.withValues(alpha: 0.1),
                    blurRadius: _isPressed ? 8 : 12,
                    offset: Offset(0, _isPressed ? 2 : 4),
                  ),
                ],
                border: Border.all(
                  color: isActive
                    ? AppTheme.successColor.withValues(alpha: 0.3)
                    : Colors.grey.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Stack(
                children: [
                  // Room number in center
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          widget.room.roomId ?? '',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: isActive ? Colors.white : AppTheme.textPrimary,
                          ),
                        ),
                        if (isActive) ...[
                          SizedBox(height: 4),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'Đang sử dụng',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Room type badge
                  if (widget.room.type?.replaceAll(' ', '') != null)
                    Positioned(
                      top: 6,
                      right: 6,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: roomTypeColor,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: roomTypeColor.withValues(alpha: 0.3),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          _getRoomTypeLabel(),
                          style: TextStyle(
                            fontSize: 8,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                  // Status indicator
                  Positioned(
                    top: 6,
                    left: 6,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: isActive ? Colors.white : Colors.grey.shade400,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: (isActive ? Colors.white : Colors.grey.shade400)
                                .withValues(alpha: 0.5),
                            blurRadius: 4,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
 