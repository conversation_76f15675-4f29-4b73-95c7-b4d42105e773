import 'package:flutter/material.dart';
import 'package:gsheets/gsheets.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/services/rooms/room_service.dart';
import 'package:songanh/widgets/expanded_card.dart';
import 'package:songanh/widgets/loading_widget.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key, required this.wSheet});
  final Worksheet wSheet;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Room>>(
        future: RoomService.getRoomInfo(widget.wSheet),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return LoadingWidget(
              message: 'Đang tải thông tin phòng...',
            );
          }
          if (snapshot.connectionState == ConnectionState.done) {
            final rooms = snapshot.data ?? [];
            if (rooms.isEmpty) {
              return EmptyStateWidget(
                title: 'Không có dữ liệu phòng',
                subtitle: 'Hiện tại chưa có thông tin phòng nào được tải',
                icon: Icons.hotel_outlined,
              );
            }
            return ExpandedCard(
              list: rooms,
            );
          }
          return EmptyStateWidget(
            title: 'Lỗi tải dữ liệu',
            subtitle: 'Không thể tải thông tin phòng. Vui lòng thử lại',
            icon: Icons.error_outline_rounded,
          );
        });
  }
}
