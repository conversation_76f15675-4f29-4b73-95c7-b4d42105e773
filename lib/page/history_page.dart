import 'dart:async';

import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:gsheets/gsheets.dart';
import 'package:intl/intl.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/page/chart_zoom.dart';
import 'package:songanh/query/query_history_page.dart';
import 'package:songanh/services/rooms/room_service.dart';
import 'package:songanh/widgets/value_notifier_list.dart';
import 'package:substring_highlight/substring_highlight.dart';

class HistoryPage extends StatefulWidget {
  const HistoryPage({super.key, required this.wSheet});
  final Worksheet wSheet;
  @override
  State<HistoryPage> createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage> {
  final ValueNotifier<FilterType?> filterType = ValueNotifier(null);
  final ValueNotifier<double?> vSlider = ValueNotifier(1);
  final ValueNotifier<RangeValues?> vRange = ValueNotifier(RangeValues(1, 3));
  ValueNotifierList<Room> vRooms = ValueNotifierList([]);
  ValueNotifier<DateTime?> startDate = ValueNotifier(null);
  ValueNotifier<DateTime?> endDate = ValueNotifier(null);
  Timer? time;
  List<Room> roomTemplate = [];
  late TextEditingController controller;
  @override
  void initState() {
    super.initState();
    controller = TextEditingController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RoomService.getRoomInfo(widget.wSheet).then((rooms) {
        roomTemplate = rooms;
        vRooms.setValue(rooms);
      }).catchError((error) {});
    });
  }

  @override
  void dispose() {
    controller.dispose();
    time?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 12, top: 8),
                child: SizedBox(
                  width: 300,
                  child: TextField(
                    controller: controller,
                    decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                                color: Colors.grey.withValues(alpha: .5))),
                        focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                                color: Theme.of(context).primaryColor)),
                        isDense: true,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        hintText: 'Tìm kiếm số điện thoại hoặc tên Kh...',
                        hintStyle: TextStyle(
                            color: Theme.of(context)
                                .hintColor
                                .withValues(alpha: .3)),
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                                color: Colors.grey.withValues(alpha: .1)))),
                    maxLines: 1,
                    style: TextStyle(
                      fontSize: 16,
                      decorationColor: Colors.white.withValues(alpha: 0),
                    ),
                    onChanged: (value) {
                      final curentRooms = roomTemplate
                          .where((final r) =>
                              (r.phone?.contains(value) ?? false) ||
                              (r.consumerName
                                      ?.toLowerCase()
                                      .contains(value.toLowerCase()) ??
                                  false))
                          .toList();
                      vRooms.setValue(curentRooms);
                    },
                  ),
                ),
              ),
              const SizedBox(
                height: 8,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ValueListenableBuilder(
                    valueListenable: filterType,
                    builder: (context, vType, child) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              DecoratedBox(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: Colors.grey.withValues(alpha: .3)),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  child: InkWell(
                                      onTapDown: (details) {
                                        final offset = details.globalPosition;
                                        showMenu(
                                            context: context,
                                            position: RelativeRect.fromLTRB(
                                                50,
                                                offset.dy + 10,
                                                MediaQuery.sizeOf(context)
                                                        .width /
                                                    2,
                                                offset.dy),
                                            items: [
                                              PopupMenuItem(
                                                child:
                                                    Text('Thời gian gần nhất'),
                                                onTap: () {
                                                  filterType.value =
                                                      FilterType.timeRecent;
                                                },
                                              ),
                                              PopupMenuItem(
                                                child: Text('khoảng thời gian'),
                                                onTap: () {
                                                  filterType.value =
                                                      FilterType.rangeTime;
                                                },
                                              ),
                                            ]);
                                      },
                                      child: Text(mapType[vType] ??
                                          '-- Chọn kiểu lọc -- ')),
                                ),
                              ),
                              if (vType == FilterType.timeRecent)
                                Row(
                                  children: [
                                    Icon(Icons.chevron_right),
                                    Expanded(
                                      child: ValueListenableBuilder(
                                          valueListenable: vSlider,
                                          builder: (context, vMounth, child) {
                                            return Text(
                                                '${vMounth?.round().toString()} tháng gần nhất ',
                                                overflow: TextOverflow.ellipsis);
                                          }),
                                    )
                                  ],
                                ),
                              if (vType == FilterType.rangeTime)
                                Row(
                                  children: [
                                    Icon(Icons.chevron_right),
                                    Expanded(
                                      child: ValueListenableBuilder(
                                          valueListenable: vRange,
                                          builder:
                                              (context, vRangeMounth, child) {
                                            return Text.rich(
                                                TextSpan(children: [
                                                  TextSpan(
                                                      text: 'từ tháng ',
                                                      children: [
                                                        TextSpan(
                                                            text:
                                                                '${vRangeMounth?.start.round()}')
                                                      ]),
                                                  TextSpan(text: ' '),
                                                  TextSpan(
                                                      text: 'đến tháng ',
                                                      children: [
                                                        TextSpan(
                                                            text:
                                                                '${vRangeMounth?.end.round()}')
                                                      ])
                                                ]),
                                                overflow: TextOverflow.ellipsis);
                                          }),
                                    )
                                  ],
                                )
                            ],
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  if (vType == FilterType.timeRecent)
                                    ValueListenableBuilder(
                                        valueListenable: vSlider,
                                        builder: (context, vMounth, child) {
                                          return Expanded(
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8),
                                              child: Slider(
                                                  label:
                                                      ' ${vMounth?.round().toString()} tháng gần nhất ',
                                                  min: 1,
                                                  max: 12,
                                                  divisions: 12,
                                                  value: vMounth ?? 1.0,
                                                  onChanged: (val) {
                                                    vSlider.value = val;
                                                  },
                                                  onChangeEnd: (final val) {
                                                    RoomService.getRoomInfo(
                                                            widget.wSheet)
                                                        .then((rooms) {
                                                      final listF = QueryHistoryPage
                                                          .filterOrdersByMonths(
                                                              rooms,
                                                              val.round());
                                                      vRooms.setValue(listF);
                                                    });
                                                  }),
                                            ),
                                          );
                                        }),
                                  if (vType == FilterType.rangeTime)
                                    ValueListenableBuilder(
                                        valueListenable: vRange,
                                        builder:
                                            (context, vRangeMounth, child) {
                                          return Expanded(
                                            child: RangeSlider(
                                                min: 1,
                                                max: 12,
                                                divisions: 12,
                                                values: vRangeMounth ??
                                                    RangeValues(1, 3),
                                                labels: RangeLabels(
                                                    vRangeMounth?.start
                                                            .round()
                                                            .toString() ??
                                                        '',
                                                    vRangeMounth?.end
                                                            .round()
                                                            .toString() ??
                                                        ''),
                                                onChanged: (final val) {
                                                  vRange.value = val;
                                                }),
                                          );
                                        }),
                                ],
                              ),
                              if (vType == FilterType.rangeTime)
                                Row(
                                  children: [
                                    IconButton(
                                        onPressed: () {
                                          showModal(
                                            context: context,
                                            builder:
                                                (builder) =>
                                                    ValueListenableBuilder(
                                                        valueListenable: vRange,
                                                        builder: (context,
                                                            vDateRange, child) {
                                                          return Center(
                                                            child: SizedBox(
                                                                width: double
                                                                    .infinity,
                                                                height: MediaQuery.sizeOf(
                                                                            context)
                                                                        .height /
                                                                    1.5,
                                                                child:
                                                                    DatePickerTheme(
                                                                  data: DatePickerThemeData(
                                                                      locale: Locale(
                                                                          'vi',
                                                                          'VN')),
                                                                  child:
                                                                      DateRangePickerDialog(
                                                                    saveText:
                                                                        'Xác nhận',
                                                                    firstDate:
                                                                        DateTime(
                                                                      DateTime.now()
                                                                          .year,
                                                                      vDateRange
                                                                              ?.start
                                                                              .round() ??
                                                                          0,
                                                                    ),
                                                                    lastDate:
                                                                        DateTime(
                                                                      DateTime.now()
                                                                          .year,
                                                                      vDateRange
                                                                              ?.end
                                                                              .round() ??
                                                                          0,
                                                                    ),
                                                                    selectableDayPredicate: (day,
                                                                        selectedStartDay,
                                                                        selectedEndDay) {
                                                                      // startDate = selectedStartDay;
                                                                      // endDate = selectedEndDay;
                                                                      return true;
                                                                    },
                                                                  ),
                                                                )),
                                                          );
                                                        }),
                                          ).then((val) {
                                            if (val != null) {
                                              final time = val.toString();
                                              final startEnd =
                                                  time.split(' - ');
                                              startDate.value =
                                                  DateFormat('yyyy-MM-dd')
                                                      .parse(startEnd.first);
                                              endDate.value =
                                                  DateFormat('yyyy-MM-dd')
                                                      .parse(startEnd.last);
                                              RoomService.getRoomInfo(
                                                      widget.wSheet)
                                                  .then((rooms) {
                                                final listF = QueryHistoryPage
                                                    .filterOrdersByDateRange(
                                                        rooms,
                                                        startDate.value ??
                                                            DateTime.now(),
                                                        endDate.value ??
                                                            DateTime.now());
                                                vRooms.setValue(listF);
                                              });
                                            }
                                          });
                                        },
                                        icon: Icon(Icons.date_range)),
                                    Icon(Icons.chevron_right),
                                    Expanded(
                                      child: Row(
                                        children: [
                                          Flexible(
                                            child: ValueListenableBuilder(
                                                valueListenable: startDate,
                                                builder: (context, vDate, child) {
                                                  return vDate == null
                                                      ? Text('--/--/----', overflow: TextOverflow.ellipsis)
                                                      : Text(
                                                          '${vDate.day}/${vDate.month}/${vDate.year}',
                                                          overflow: TextOverflow.ellipsis);
                                                }),
                                          ),
                                          Flexible(
                                            child: ValueListenableBuilder(
                                                valueListenable: endDate,
                                                builder: (context, vDate, child) {
                                                  return vDate == null
                                                      ? Text('  --/--/----', overflow: TextOverflow.ellipsis)
                                                      : Text(
                                                          ' - ${vDate.day}/${vDate.month}/${vDate.year}',
                                                          overflow: TextOverflow.ellipsis);
                                                }),
                                          ),
                                        ],
                                      ),
                                    ),
                                    IconButton(
                                        onPressed: () {
                                            showModal(
                                          context: context,
                                          builder: (builder) => Padding(
                                            padding: const EdgeInsets
                                            .symmetric(horizontal: 2),
                                            child: Container(
                                              color: Colors.white,
                                              width: MediaQuery.sizeOf(
                                                      context)
                                                  .width +
                                              100,
                                               height: MediaQuery.sizeOf(
                                                      context)
                                                  .height,
                                            child: ChartPage(rooms: vRooms.value,)),
                                          ));
                                        },
                                        icon: Icon(Icons.bar_chart))
                                  ],
                                ),
                              if (vType == FilterType.timeRecent)
                                IconButton(
                                    onPressed: () {
                                      showModal(
                                          context: context,
                                          builder: (builder) => Padding(
                                            padding: const EdgeInsets
                                            .symmetric(horizontal: 2),
                                            child: Container(
                                              color: Colors.white,
                                              width: MediaQuery.sizeOf(
                                                      context)
                                                  .width +
                                              100,
                                               height: MediaQuery.sizeOf(
                                                      context)
                                                  .height,
                                            child: ChartPage(rooms: vRooms.value,)),
                                          ));
                                    },
                                    icon: Icon(Icons.bar_chart))
                            ],
                          )
                        ],
                      );
                    }),
              )
            ],
          ),
          ValueListenableBuilder(
              valueListenable: vRooms,
              builder: (context, vRoom, child) {
                if (vRoom.isEmpty) {
                  return Center(
                    child: Text('Không có dữ liệu'),
                  );
                }
                return Expanded(
                    child: ValueListenableBuilder(
                        valueListenable: controller,
                        builder: (context, vText, child) {
                          return ListView.builder(
                            itemCount: vRoom.length,
                            itemBuilder: (context, index) {
                              return DecoratedBox(
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(color: Colors.grey.withValues(alpha: .5))
                                  )
                                ),
                                child: ListTile(
                                  title: Text.rich(
                                    TextSpan(
                                      text: vRoom[index].roomId ?? '',
                                      children: [
                                        WidgetSpan(
                                            child: SizedBox(
                                          width: 8,
                                        )),
                                        if (vRoom[index].type == 'VIP')
                                          WidgetSpan(
                                            alignment:
                                                PlaceholderAlignment.middle,
                                            child: DecoratedBox(
                                              decoration: BoxDecoration(
                                                  color: Colors.yellow,
                                                  borderRadius:
                                                      BorderRadius.circular(4)),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8),
                                                child: Text('VIP'),
                                              ),
                                            ),
                                          ),
                                        WidgetSpan(
                                            alignment:
                                                PlaceholderAlignment.middle,
                                            child: Padding(
                                              padding: const EdgeInsets.symmetric(
                                                  horizontal: 8),
                                              child: DecoratedBox(
                                                  decoration: BoxDecoration(
                                                      color: Colors.black,
                                                      shape: BoxShape.circle),
                                                  child: SizedBox(
                                                    width: 4,
                                                    height: 4,
                                                  )),
                                            )),
                                        TextSpan(
                                            text: vRoom[index].time,
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .primary)),
                                        WidgetSpan(
                                            alignment:
                                                PlaceholderAlignment.middle,
                                            child: Padding(
                                              padding: const EdgeInsets.symmetric(
                                                  horizontal: 8),
                                              child: DecoratedBox(
                                                  decoration: BoxDecoration(
                                                      color: Colors.black,
                                                      shape: BoxShape.circle),
                                                  child: SizedBox(
                                                    width: 4,
                                                    height: 4,
                                                  )),
                                            )),
                                        if (vRoom[index].gia != null)
                                          TextSpan(
                                              text:
                                                  '${NumberFormat('#,###', 'vi_VN').format(int.tryParse(vRoom[index].gia ?? '') ?? 0)} VND')
                                      ]),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  subtitle: Column(
                                    spacing: 6,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        spacing: 8,
                                        children: [
                                          Icon(Icons.person, size: 16,),
                                          Expanded(
                                            child: SubstringHighlight(
                                              text: vRoom[index].consumerName ?? '',
                                              term: vText.text,
                                              textStyleHighlight: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.copyWith(
                                                          color: Colors.green,
                                                          backgroundColor: Colors.green
                                                              .withValues(alpha: .2)) ??
                                                  TextStyle(
                                                      fontSize: 16,
                                                      color: Colors.green),
                                            ),
                                          ),
                                        ],
                                      ),
                                  if(vRoom[index].phone?.isNotEmpty?? false)
                                   Row(
                                        spacing: 8,
                                        children: [
                                          Icon(Icons.phone,  size: 16),
                                          Expanded(
                                            child: SubstringHighlight(
                                              text: vRoom[index].phone ?? '',
                                              term: vText.text,
                                              textStyleHighlight: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.copyWith(
                                                          color: Colors.green,
                                                          backgroundColor: Colors.green
                                                              .withValues(alpha: .2)) ??
                                                  TextStyle(
                                                      fontSize: 16,
                                                      color: Colors.green),
                                            ),
                                          ),
                                        ],
                                      ),
                                      _buildTine(
                                          'Nhận phòng: ', vRoom[index].timeIn ?? ''),
                                      // _buildTine(
                                      //     'Trả: ', vRoom[index].timeOut ?? '')
                                    ],
                                  ),
                                  // Text('Đặt vào: ${vRoom[index].timeIn}'),
                                ),
                              );
                            },
                          );
                        }));
              })
        ],
      ),
    );
  }

  Map<FilterType, String> mapType = {
    FilterType.timeRecent: "Thời gian gần nhất",
    FilterType.rangeTime: "Khoảng thời gian",
  };
  Text _buildTine(
    String title,
    String time,
  ) {
    DateTime dateTime = DateFormat('HH:mm dd/MM/yyyy').parse(time).toLocal();
    return Text.rich(TextSpan(
        text: title,
        style: TextStyle(fontWeight: FontWeight.normal),
        children: [
          TextSpan(
            text: DateFormat('HH:mm - dd/MM/yyyy').format(dateTime),
            style: TextStyle(
                fontSize: 18,
                backgroundColor: Colors.grey.withValues(alpha: .1),
                fontWeight: FontWeight.bold),
          )
        ]));
  }
}

enum FilterType {
  timeRecent,
  rangeTime,
}
