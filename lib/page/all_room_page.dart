import 'package:flutter/material.dart';
import 'package:gsheets/gsheets.dart';
import 'package:songanh/models/room.dart';
import 'package:songanh/services/rooms/room_service.dart';
import 'package:songanh/widgets/all_room_body.dart';
import 'package:songanh/widgets/loading_widget.dart';
import 'package:songanh/theme.dart';

class AllRoomPage extends StatelessWidget {
  const AllRoomPage({super.key, required this.worksheet});
  final Worksheet worksheet;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'Tổng quan sơ đồ',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
          centerTitle: true,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
            ),
          ),
          iconTheme: IconThemeData(color: Colors.white),
        ),
        body: FutureBuilder<List<Room>>(
            future: RoomService.getRoomInfo(worksheet),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return LoadingWidget(
                  message: 'Đang tải sơ đồ phòng...',
                );
              }
              if (snapshot.connectionState == ConnectionState.done) {
                final rooms = snapshot.data ?? [];
                if (rooms.isEmpty) {
                  return EmptyStateWidget(
                    title: 'Không có dữ liệu phòng',
                    subtitle: 'Hiện tại chưa có thông tin phòng nào',
                    icon: Icons.apartment_outlined,
                  );
                }
                return AllRoomBody(
                  list: rooms,
                );
              }
              return EmptyStateWidget(
                title: 'Lỗi tải dữ liệu',
                subtitle: 'Không thể tải sơ đồ phòng. Vui lòng thử lại',
                icon: Icons.error_outline_rounded,
              );
            }));
  }
}
