import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:gsheets/gsheets.dart';
import 'package:retry/retry.dart';
import 'package:songanh/config/gsheet.dart';
import 'package:songanh/page/history_page.dart';
import 'package:songanh/page/home_page.dart';
import 'package:songanh/routes.dart';
import 'package:songanh/theme.dart';
import 'package:songanh/widgets/loading_widget.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  final remoteConfig = FirebaseRemoteConfig.instance;
  await remoteConfig.setConfigSettings(RemoteConfigSettings(
    fetchTimeout: const Duration(minutes: 1),
    minimumFetchInterval: const Duration(hours: 1),
  ));
// await remoteConfig.setDefaults(const {
//     "value": '123',
//     "value2": '3.14159',
//     "value3": 'true',
//     "value4": "Hello, world!",
// });
  await retry<bool>(
    // Make a GET request
    () => FirebaseRemoteConfig.instance
        .fetchAndActivate()
        .timeout(const Duration(seconds: 60)),
    // Retry on FirebaseException or TimeoutException
    retryIf: (e) => e is FirebaseException || e is TimeoutException,
  );
  await FirebaseRemoteConfig.instance.setDefaults(const {
    "example_param_1": 42,
    "example_param_2": 3.14159,
    "example_param_3": true,
    "testapp": "Hello, world!",
  });

  final spreadsheetId = remoteConfig.getString('spreadsheetId');
  final credentialsJson = remoteConfig.getValue('keygsheet');
  GsheetConfig.gsheets = GSheets(credentialsJson.asString());
  GsheetConfig.spreadsheet =
      await GsheetConfig.gsheets.spreadsheet(spreadsheetId);

  runApp(MyApp(
    spreadsheetId: spreadsheetId,
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key, required this.spreadsheetId});
  static bool firstTimeOneTab = false;
  static bool firstTimeSecondTab = false;
  final String spreadsheetId;
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      locale: const Locale('vi', 'VN'),
      supportedLocales: const [
        Locale('vi', 'VN'),
        Locale('en', 'US'), 
      ],
      localizationsDelegates: const [
        // Các delegate mặc định của Flutter
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      title: 'Flutter Demo',
      theme: AppTheme.theme,
      home: FutureBuilder<List<Worksheet>>(
          future: GsheetConfig.initListWork(spreadsheetId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return LoadingWidget(
                message: 'Đang tải dữ liệu...',
                size: 60,
              );
            }
            if (snapshot.connectionState == ConnectionState.done) {
              return MyHomePage(
                works: snapshot.data ?? [],
              );
            }
            return EmptyStateWidget(
              title: 'Không thể tải dữ liệu',
              subtitle: 'Vui lòng kiểm tra kết nối mạng và thử lại',
              icon: Icons.error_outline_rounded,
            );
          }),
    );
  }
}

class MyHomePage extends StatelessWidget {
  const MyHomePage({super.key, required this.works});
  final List<Worksheet> works;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: AppTheme.primaryGradient,
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryBlue.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: FloatingActionButton.extended(
          backgroundColor: Colors.transparent,
          elevation: 0,
          icon: Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.dashboard_rounded,
              color: Colors.white,
              size: 20,
            ),
          ),
          label: Text(
            'Tổng quan sơ đồ',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          onPressed: () {
            AppNavigator.pushAllRoom(context, works.first);
          },
        ),
      ),
      body: DefaultTabController(
        initialIndex: 0,
        length: works.length,
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              'Quản lý khách sạn',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w700,
                color: Colors.white,
              ),
            ),
            centerTitle: true,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
            ),
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(48),
              child: Container(
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                ),
                child: TabBar(
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicatorPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  indicator: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
                  labelStyle: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                  tabs: [
                    ...List.generate(works.length, (i) {
                      final tab = works[i].title;
                      return Tab(
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                i == 0 ? Icons.home_rounded : Icons.history_rounded,
                                size: 18,
                              ),
                              SizedBox(width: 8),
                              Text(tab),
                            ],
                          ),
                        ),
                      );
                    })
                  ],
                ),
              ),
            ),
          ),
          body: TabBarView(children: [
            HomePage(
              wSheet: works.first,
            ),
            HistoryPage(
              wSheet: works.last,
            )
          ]),
        ),
      ),
    );
  }
}
